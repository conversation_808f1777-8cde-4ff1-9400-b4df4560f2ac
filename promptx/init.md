# Web-to-Static Enterprise 项目初始化提示词

## 项目概述
请帮我初始化一个名为 `web-to-static-enterprise` 的企业级静态网站生成器项目。这是一个基于 Rust 的大型企业级解决方案，具有反爬虫对抗、双引擎架构、分布式处理等核心功能。

## 项目结构要求
请按照以下目录结构创建完整的 Rust 工作空间项目：

```
web-to-static-enterprise/
├── Cargo.toml                    # 工作空间配置
├── README.md                     # 项目说明
├── src/
│   ├── main.rs                   # 主程序入口
│   ├── lib.rs                    # 库入口
│   ├── core/                     # 核心基础模块
│   │   ├── mod.rs
│   │   ├── config/               # 配置管理
│   │   ├── error/                # 错误处理
│   │   ├── logging/              # 日志系统
│   │   └── resource/             # 资源管理
│   ├── anti_bot/                 # 反爬虫对抗模块
│   │   ├── mod.rs
│   │   ├── proxy_pool/           # 代理池
│   │   ├── ua_pool/              # UA池
│   │   ├── captcha/              # 验证码处理
│   │   ├── behavior/             # 行为模拟
│   │   └── rate_limit/           # 频率控制
│   ├── engines/                  # 双引擎系统
│   │   ├── mod.rs
│   │   ├── http/                 # HTTP引擎
│   │   ├── browser/              # 浏览器引擎
│   │   ├── router/               # 智能路由
│   │   └── coordinator/          # 引擎协调
│   ├── auth/                     # 认证系统
│   │   ├── mod.rs
│   │   ├── multi_auth/           # 多重认证
│   │   ├── session/              # 会话管理
│   │   ├── mfa/                  # 多因素认证
│   │   └── sso/                  # 单点登录
│   ├── monitor/                  # 流量监控
│   │   ├── mod.rs
│   │   ├── traffic/              # 流量捕获
│   │   ├── protocol/             # 协议解析
│   │   ├── analytics/            # 实时分析
│   │   └── storage/              # 流量存储
│   ├── parser/                   # 内容解析
│   │   ├── mod.rs
│   │   ├── html/                 # HTML解析
│   │   ├── css/                  # CSS解析
│   │   ├── js/                   # JavaScript解析
│   │   ├── api/                  # API数据解析
│   │   └── binary/               # 二进制解析
│   ├── staticizer/               # 静态化系统
│   │   ├── mod.rs
│   │   ├── builder/              # 静态构建
│   │   ├── rewriter/             # 链接重写
│   │   ├── optimizer/            # 资源优化
│   │   └── bundler/              # 资源打包
│   ├── crawler/                  # 爬虫系统
│   │   ├── mod.rs
│   │   ├── scheduler/            # 任务调度
│   │   ├── worker/               # 工作节点
│   │   ├── coordinator/          # 协调器
│   │   └── balancer/             # 负载均衡
│   ├── management/               # 管理系统
│   │   ├── mod.rs
│   │   ├── web_ui/               # Web界面
│   │   ├── api/                  # API接口
│   │   ├── auth/                 # 权限管理
│   │   └── monitoring/           # 监控面板
│   └── plugins/                  # 插件系统
│       ├── mod.rs
│       ├── protocol/             # 协议插件
│       ├── auth/                 # 认证插件
│       └── processor/            # 处理器插件
├── tests/                        # 测试文件
├── benchmarks/                   # 性能测试
├── examples/                     # 示例代码
├── docs/                         # 文档
├── configs/                      # 配置文件
├── scripts/                      # 部署脚本
└── docker/                       # Docker配置
```

## 依赖配置要求
请在 Cargo.toml 中配置以下核心依赖：

```toml
[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }
# HTTP客户端
reqwest = { version = "0.11", features = ["json", "cookies", "socks"] }
# 无头浏览器
headless_chrome = "1.0"
# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
# 配置管理
config = "0.13"
# 日志系统
tracing = "0.1"
tracing-subscriber = "0.3"
# 错误处理
anyhow = "1.0"
thiserror = "1.0"
# 数据库
sqlx = { version = "0.7", features = ["postgres", "runtime-tokio-rustls"] }
redis = "0.23"
# 认证
oauth2 = "4.4"
jsonwebtoken = "8.3"
# 网络代理
proxy-pool = "0.3"
# HTML解析
scraper = "0.18"
# 正则表达式
regex = "1.10"
```

## 初始化任务
1. 创建完整的目录结构
2. 生成所有必要的 mod.rs 文件
3. 创建基础的 main.rs 和 lib.rs
4. 配置 Cargo.toml 工作空间
5. 为每个模块创建基础的结构体和trait定义
6. 添加基础的错误处理和日志配置
7. 创建示例配置文件

## 代码风格要求
- 使用标准的 Rust 命名约定
- 添加完整的文档注释
- 实现适当的错误处理
- 使用 async/await 异步编程模式
- 遵循企业级代码质量标准

## 特殊要求
- 所有模块都应该是可插拔和可配置的
- 实现统一的配置管理系统
- 添加完整的日志和监控支持
- 考虑分布式部署的架构设计
- 预留插件系统的扩展接口

请帮我创建这个项目的初始化代码结构，重点关注模块化设计和企业级架构模式。